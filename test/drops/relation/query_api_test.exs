defmodule Drops.Relations.QueryAPISpec do
  use Drops.RelationCase, async: false

  describe "query API functions" do
    @tag relations: [:users]
    test "query functions work with actual data", %{users: relation} do
      # Test count on empty table
      assert relation.count() == 0

      # Test all on empty table
      assert relation.all() == []

      # Insert a user using Ecto changeset to handle timestamps properly
      user_struct = relation.struct(%{name: "Test User", email: "<EMAIL>"})
      changeset = Ecto.Changeset.cast(user_struct, %{}, [:name, :email])
      {:ok, _} = relation.insert(changeset)

      # Get the inserted user to test with
      [user] = relation.all()
      assert user.name == "Test User"

      # Test count after insert
      assert relation.count() == 1

      # Test get
      found_user = relation.get(user.id)
      assert found_user.name == "Test User"
      assert found_user.email == "<EMAIL>"

      # Test get_by
      found_by_email = relation.get_by(email: "<EMAIL>")
      assert found_by_email.id == user.id

      # Test all
      all_users = relation.all()
      assert length(all_users) == 1
      assert hd(all_users).id == user.id

      # Test update (using changeset)
      changeset = Ecto.Changeset.change(user, %{name: "Updated User"})
      {:ok, updated_user} = relation.update(changeset)
      assert updated_user.name == "Updated User"

      # Test delete
      {:ok, _deleted_user} = relation.delete(updated_user)
      assert relation.count() == 0
    end
  end

  describe "index-based finders" do
    @tag relations: [:users]
    test "generates get_by_{field} functions for indexed fields", %{users: users} do
      user_struct = users.struct(%{name: "testuser", email: "<EMAIL>"})
      changeset = Ecto.Changeset.cast(user_struct, %{}, [:name, :email])

      {:ok, inserted_user} = users.insert(changeset)

      # Test the finders
      user_by_email = users.get_by_email("<EMAIL>")
      assert user_by_email != nil
      assert user_by_email.name == "testuser"

      user_by_name = users.get_by_name("testuser")
      assert user_by_name != nil
      assert user_by_name.email == "<EMAIL>"
      assert user_by_name.id == inserted_user.id
    end
  end

  describe "nested Schema module" do
    @tag relations: [:users]
    test "generates proper Ecto.Schema module", %{users: users} do
      # Test that the Struct module exists and behaves like an Ecto.Schema
      struct_module_name = Module.concat(users, Struct)
      assert Code.ensure_loaded?(struct_module_name)

      # Test Ecto.Schema functions
      assert users.ecto_schema(:source) == "users"
      assert :id in users.ecto_schema(:fields)
      assert :name in users.ecto_schema(:fields)
      assert :email in users.ecto_schema(:fields)

      # Test that we can create structs (using apply to avoid compile-time issues)
      user_struct = struct(struct_module_name, %{name: "Test", email: "<EMAIL>"})
      assert user_struct.name == "Test"
      assert user_struct.email == "<EMAIL>"

      # Test that the struct works with Ecto.Repo functions
      {:ok, inserted_user} = users.insert(user_struct)
      assert inserted_user.name == "Test"
      assert inserted_user.email == "<EMAIL>"
    end
  end

  describe "parent module schema() function" do
    @tag relations: [:users]
    test "provides access to Drops.Relation.Schema", %{users: users} do
      schema = users.schema()
      assert schema.__struct__ == Drops.Relation.Schema
      assert schema.source == "users"
      assert length(schema.fields) > 0

      # Check that fields contain expected field structs
      field_names = Enum.map(schema.fields, & &1.name)
      assert :id in field_names
      assert :name in field_names
      assert :email in field_names
    end
  end
end
