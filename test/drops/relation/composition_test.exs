defmodule Drops.Relations.CompositionTest do
  use Drops.RelationCase, async: false

  @tag relations: [:users]
  test "composing multiple relation functions", %{users: users} do
    users.insert(%{name: "<PERSON>", email: "<EMAIL>"})
    users.insert(%{name: "<PERSON>", email: "<EMAIL>"})

    relation =
      users |> users.restrict(name: "<PERSON>") |> users.restrict(email: "<EMAIL>")

    assert jane = List.first(relation)

    assert jane.name == "<PERSON>"
    assert jane.email == "<EMAIL>"
  end
end
