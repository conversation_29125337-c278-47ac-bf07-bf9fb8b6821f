defmodule Drops.Relations.CompositionTest do
  use Drops.RelationCase, async: false

  @tag relations: [:users]
  test "composing multiple relation functions", %{users: users} do
    users.insert(%{name: "<PERSON>", email: "<EMAIL>"})
    users.insert(%{name: "<PERSON>", email: "<EMAIL>"})

    relation =
      users |> users.restrict(name: "<PERSON>") |> users.restrict(email: "<EMAIL>")

    assert jane = Enum.at(relation, 0)

    assert jane.name == "<PERSON>"
    assert jane.email == "<EMAIL>"
  end

  @tag relations: [:users]
  test "enumerable protocol works with various functions", %{users: users} do
    users.insert(%{name: "<PERSON>", email: "<EMAIL>"})
    users.insert(%{name: "<PERSON>", email: "<EMAIL>"})
    users.insert(%{name: "<PERSON>", email: "<EMAIL>"})

    relation = users |> users.restrict(name: "<PERSON>")

    # Test Enum.count
    assert Enum.count(relation) == 1

    # Test Enum.map
    emails = Enum.map(relation, & &1.email)
    assert emails == ["<EMAIL>"]

    # Test Enum.filter (should work on the materialized list)
    filtered = Enum.filter(relation, fn user -> String.contains?(user.email, "alice") end)
    assert length(filtered) == 1

    # Test Enum.any?
    assert Enum.any?(relation, fn user -> user.name == "Alice" end)
    refute Enum.any?(relation, fn user -> user.name == "Bob" end)
  end

  @tag relations: [:users]
  test "enumerable protocol works with empty results", %{users: users} do
    users.insert(%{name: "Alice", email: "<EMAIL>"})

    # Create a relation that should return no results
    relation = users |> users.restrict(name: "NonExistent")

    # Test with empty results
    assert Enum.count(relation) == 0
    assert Enum.to_list(relation) == []
    assert Enum.at(relation, 0) == nil
    refute Enum.any?(relation, fn _ -> true end)
  end
end
